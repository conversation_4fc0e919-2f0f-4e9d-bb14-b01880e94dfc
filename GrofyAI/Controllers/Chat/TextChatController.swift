import Combine
import Foundation
import SwiftUI

@MainActor
final class TextChatController: ObservableObject {
    /// 消息列表
    @Published private(set) var stableMessages: [ChatMessageModel] = []

    /// 当前流式消息（独立管理，高频更新）
    @Published private(set) var streamingMessage: ChatMessageModel?

    /// 流式内容缓冲区（用于增量更新）
    @Published private(set) var streamingContent = ""

    // MARK: - UI 状态管理

    @Published var inputText = ""
    @Published var isLoading = false
    @Published var loadingType: ChatLoadingType = .none
    @Published var errorMessage = ""
    @Published var showError = false

    // MARK: - 文本聊天专用功能

    @Published var selectedModelId = 1000000
    @Published var availableModels: [LLMRes] = []
    @Published var enableThinking = false
    @Published var enableNetworking = false

    // MARK: - 内部组件

    private let streamingManager = StreamingContentManager()
    private let messageManager = MessageDataManager()

    // MARK: - 核心服务依赖

    private let chatService = ChatService()
    private let threadManager = ThreadManager.shared
    private let modelManager = ModelManager.shared

    // MARK: - 流式处理状态

    private var currentStreamTask: Task<Void, Never>?
    private var currentStreamingMessageId: UUID?

    // MARK: - 重试机制状态

    private var isRetryingMessage = false
    private var retryingMessageId: UUID?
    private var retryingVariantIndex: Int?

    init() {
        setupModelManager()
        setupStreamingManager()
    }

    deinit {
        currentStreamTask?.cancel()
        currentStreamTask = nil
        streamingManager.reset()
        NotificationCenter.default.removeObserver(self)
    }

    /// 发送文本消息
    func sendMessage() {
        let trimmedInput = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedInput.isEmpty else { return }

        let userMessage = trimmedInput
        inputText = ""

        addUserMessage(content: userMessage)
        startStreamingResponse(for: userMessage)
    }

    /// 处理初始消息（从其他页面跳转过来）
    func processInitialMessage(_ message: String) {
        guard !message.isEmpty else { return }

        inputText = message
        sendMessage()
    }

    /// 重试消息
    func retryMessage(messageId: UUID) {
        guard let messageIndex = findMessageIndex(by: messageId),
              !stableMessages[messageIndex].isUser
        else { return }

        let messageToRetry = stableMessages[messageIndex]
        setupRetryState(for: messageToRetry)

        if let userMessageIndex = findPreviousUserMessage(before: messageIndex) {
            let userMessage = stableMessages[userMessageIndex].content
            startRetryRequest(for: userMessage, messageId: messageId)
        }
    }

    /// 切换深度思考模式
    func toggleThinking() {
        enableThinking.toggle()

        if enableThinking, enableNetworking {
            enableNetworking = false
        }
    }

    /// 切换联网搜索模式
    func toggleNetworking() {
        enableNetworking.toggle()

        if enableNetworking, enableThinking {
            enableThinking = false
        }
    }

    /// 停止当前流式响应
    func stopStreaming() {
        currentStreamTask?.cancel()
        currentStreamTask = nil

        Task {
            await EventSourceAdapter.shared.disconnect()
            await MainActor.run {
                self.streamingManager.forceFlush { [weak self] remainingContent in
                    if !remainingContent.isEmpty {
                        self?.updateStreamingMessageContent(remainingContent)
                    }
                }

                self.isLoading = false
                self.loadingType = .none
                self.streamingContent = ""
                self.finalizeCurrentStreamingMessage()
            }
        }
    }

    // MARK: - 消息操作接口

    /// 复制消息内容
    func copyMessage(_ message: ChatMessageModel) {
        UIPasteboard.general.string = message.content
        ToastManager.shared.showSuccess("消息已复制")
    }

    /// 分享消息内容
    func shareMessage(_ message: ChatMessageModel) {
        // TODO: 实现分享功能
    }

    /// 清空消息列表
    func clearMessages() {
        stableMessages.removeAll()
        streamingMessage = nil
        streamingContent = ""
        currentStreamingMessageId = nil

        // 重置相关状态
        isLoading = false
        loadingType = .none
        errorMessage = ""
        showError = false
    }

    /// 加载历史聊天记录
    func loadHistoryChat(threadId: String) {
        print("🔍 TextChatController Debug - loadHistoryChat called with threadId: \(threadId)")
        stopStreaming()

        // 原子性状态更新：先设置加载状态，再清空消息
        isLoading = true
        loadingType = .loadingHistory
        print("🔍 TextChatController Debug - Set loadingType to: \(loadingType), isLoading: \(isLoading)")

        // 重置状态
        stableMessages.removeAll()
        streamingMessage = nil
        streamingContent = ""
        currentStreamingMessageId = nil
        errorMessage = ""
        showError = false
        print("🔍 TextChatController Debug - Messages cleared, count: \(stableMessages.count)")

        // 清空缓存
        messageManager.clearCache()

        Task {
            await performLoadHistoryChat(threadId: threadId)
        }
    }

    /// 执行历史聊天记录加载
    private func performLoadHistoryChat(threadId: String) async {
        print("🔍 TextChatController Debug - performLoadHistoryChat started")
        do {
            let historyItems = try await chatService.getHistoryDetail(threadId: threadId, graphId: "chatbot_graph")
            print("🔍 TextChatController Debug - API call completed, historyItems count: \(historyItems.count)")

            await MainActor.run {
                let chatMessages = convertHistoryItemsToChatMessages(historyItems)
                print("🔍 TextChatController Debug - Converted to chatMessages count: \(chatMessages.count)")

                // 分离稳定消息和流式消息
                self.stableMessages = chatMessages
                self.streamingMessage = nil

                // 一次性重建索引缓存（历史消息加载完成后）
                self.messageManager.updateIndexCache(for: chatMessages)

                // 设置当前线程
                self.threadManager.setCurrentThread(threadId: threadId, isFirst: false)

                self.isLoading = false
                self.loadingType = .none
                print("🔍 TextChatController Debug - Loading completed, loadingType: \(self.loadingType), messages count: \(self.stableMessages.count)")
            }

        } catch {
            await MainActor.run {
                self.isLoading = false
                self.loadingType = .none

                let errorMsg = if let businessError = error as? BusinessError {
                    businessError.message
                } else {
                    error.localizedDescription
                }

                self.errorMessage = "加载历史对话失败: \(errorMsg)"
                self.showError = true
                print("❌ TextChatController: 加载历史对话失败 - \(error)")
            }
        }
    }

    /// 转换历史详情项为ChatMessage格式
    private func convertHistoryItemsToChatMessages(_ historyItems: [HistoryDetailItem]) -> [ChatMessageModel] {
        var chatMessages: [ChatMessageModel] = []

        for item in historyItems {
            let messages = item.toChatMessages(chatMode: .agent)
            chatMessages.append(contentsOf: messages)
        }

        return chatMessages
    }

    /// 重置当前线程
    func resetCurrentThread() {
        threadManager.clearCurrentSession()
    }

    /// 获取消息变体信息
    func getMessageVariantInfo(messageId: UUID) -> (current: Int, total: Int)? {
        guard let message = findMessage(by: messageId),
              let variants = message.variants
        else {
            return nil
        }

        return (current: message.currentVariantIndex, total: variants.count)
    }

    /// 检查是否正在重试指定消息
    func isRetryingMessage(messageId: UUID) -> Bool {
        return isRetryingMessage && retryingMessageId == messageId
    }

    /// 检查是否正在重试任何消息
    func isCurrentlyRetrying() -> Bool {
        return isRetryingMessage
    }

    /// 获取当前重试的消息ID（仅用于UI）
    var currentRetryingMessageId: UUID? {
        return isRetryingMessage ? retryingMessageId : nil
    }

    /// 获取重试期间的变体信息
    func getRetryVariantInfo() -> (current: Int, total: Int)? {
        guard isRetryingMessage,
              let retryingId = retryingMessageId,
              let message = findMessage(by: retryingId)
        else {
            return nil
        }

        let existingVariantsCount = message.variants?.count ?? 1
        let totalCount = existingVariantsCount + 1

        return (current: totalCount - 1, total: totalCount)
    }

    /// 切换消息变体（支持正常和重试场景）
    func switchMessageVariant(messageId: UUID, variantIndex: Int) {
        guard let messageIndex = findMessageIndex(by: messageId),
              let variants = stableMessages[messageIndex].variants,
              variantIndex < variants.count
        else {
            return
        }

        let selectedVariant = variants[variantIndex]
        let updatedMessage = createMessageFromVariant(
            originalMessage: stableMessages[messageIndex],
            variant: selectedVariant,
            variantIndex: variantIndex
        )

        stableMessages[messageIndex] = updatedMessage

        // 如果是重试场景，记录用户选择
        if isRetryingMessage, retryingMessageId == messageId {
            retryingVariantIndex = variantIndex
        }
    }

    // MARK: - 兼容性接口（保持与现有 UI 的兼容）

    /// 获取所有消息（用于 ChatList 显示）
    var messages: [ChatMessageModel] {
        var allMessages = stableMessages

        // 如果有流式消息，添加到末尾
        if let streaming = streamingMessage {
            allMessages.append(streaming)
        }

        return allMessages
    }
}

// MARK: - 私有实现方法

extension TextChatController {
    private func setupModelManager() {
        syncWithModelManager()
        setupModelManagerObserver()
    }

    private func syncWithModelManager() {
        availableModels = modelManager.availableModels

        // 确保选中的模型在可用列表中
        if !availableModels.contains(where: { $0.id == selectedModelId }) {
            selectedModelId = availableModels.first?.id ?? 1000000
        }
    }

    private func setupModelManagerObserver() {
        NotificationCenter.default.addObserver(
            forName: .modelListUpdated,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.syncWithModelManager()
            }
        }
    }

    private func setupStreamingManager() {
        streamingManager.reset()
    }

    private func cleanup() {
        currentStreamTask?.cancel()
        streamingManager.reset()
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - 消息管理

    private func addUserMessage(content: String) {
        let userMessage = ChatMessageModel(
            content: content,
            isUser: true,
            chatMode: .agent
        )

        let newIndex = stableMessages.count
        stableMessages.append(userMessage)

        messageManager.addMessageToCache(message: userMessage, at: newIndex)
    }

    private func createStreamingMessage() -> ChatMessageModel {
        return ChatMessageModel(
            content: "",
            isUser: false,
            type: .text,
            modelId: selectedModelId,
            chatMode: .agent
        )
    }

    private func finalizeCurrentStreamingMessage() {
        guard let streaming = streamingMessage else { return }

        // 将流式消息转换为稳定消息
        let finalMessage = ChatMessageModel(
            id: streaming.id,
            content: streaming.content,
            isUser: false,
            type: streaming.type,
            modelId: streaming.modelId,
            searchResults: streaming.searchResults,
            files: streaming.files,
            images: streaming.images,
            reasoningContent: streaming.reasoningContent,
            toolCallStatus: streaming.toolCallStatus,
            isToolActive: streaming.isToolActive,
            variants: streaming.variants,
            currentVariantIndex: streaming.currentVariantIndex,
            chatMode: streaming.chatMode
        )

        let newIndex = stableMessages.count
        stableMessages.append(finalMessage)

        // 使用高效的增量缓存更新
        messageManager.addMessageToCache(message: finalMessage, at: newIndex)

        streamingMessage = nil
        currentStreamingMessageId = nil
        streamingContent = ""
    }

    // MARK: - 消息查找辅助方法

    private func findMessage(by id: UUID) -> ChatMessageModel? {
        if let index = findMessageIndex(by: id) {
            return stableMessages[index]
        }
        return nil
    }

    private func findMessageIndex(by id: UUID) -> Int? {
        return messageManager.findMessageIndex(by: id, in: stableMessages)
    }

    private func findPreviousUserMessage(before index: Int) -> Int? {
        for i in stride(from: index - 1, through: 0, by: -1) {
            if stableMessages[i].isUser {
                return i
            }
        }
        return nil
    }

    private func createMessageFromVariant(
        originalMessage: ChatMessageModel,
        variant: MessageVariantModel,
        variantIndex: Int
    ) -> ChatMessageModel {
        return ChatMessageModel(
            id: originalMessage.id,
            content: variant.content,
            isUser: originalMessage.isUser,
            type: originalMessage.type,
            modelId: variant.modelId,
            searchResults: variant.searchResults,
            files: variant.files,
            images: variant.images,
            reasoningContent: variant.thinkingContent,
            toolCallStatus: originalMessage.toolCallStatus,
            isToolActive: originalMessage.isToolActive,
            variants: originalMessage.variants,
            currentVariantIndex: variantIndex,
            chatMode: originalMessage.chatMode
        )
    }

    // MARK: - 流式处理核心逻辑

    private func startStreamingResponse(for message: String) {
        isLoading = true
        loadingType = .streaming
        errorMessage = ""
        showError = false

        streamingMessage = createStreamingMessage()
        currentStreamingMessageId = streamingMessage?.id
        streamingContent = ""

        if !threadManager.hasActiveSession() {
            threadManager.startNewSession()
        }

        Task {
            await sendStreamingRequest(message: message)
        }
    }

    private func sendStreamingRequest(message: String) async {
        currentStreamTask?.cancel()

        let request = createChatRequest(message: message)

        currentStreamTask = Task { @MainActor in
            defer {
                currentStreamTask = nil
            }

            let stream = chatService.startChatStream(req: request)

            for await response in stream {
                if Task.isCancelled {
                    await EventSourceAdapter.shared.disconnect()
                    break
                }

                handleStreamResponse(response)
            }

            handleStreamCompleted()
        }
    }

    private func startRetryRequest(for message: String, messageId: UUID) {
        isLoading = true
        loadingType = .streaming

        streamingMessage = createStreamingMessage()
        currentStreamingMessageId = streamingMessage?.id
        streamingContent = ""

        Task {
            await sendRetryRequest(message: message)
        }
    }

    private func sendRetryRequest(message: String) async {
        currentStreamTask?.cancel()

        let request = createRetryRequest(message: message)

        currentStreamTask = Task { @MainActor in
            defer {
                currentStreamTask = nil
            }

            let stream = chatService.startChatStream(req: request)

            for await response in stream {
                if Task.isCancelled {
                    await EventSourceAdapter.shared.disconnect()
                    break
                }

                handleStreamResponse(response)
            }

            handleRetryCompleted()
        }
    }

    // MARK: - 请求创建方法

    private func createChatRequest(message: String) -> UnifiedChatCompletionReq {
        let threadId = threadManager.getCurrentThreadId()
        let isFirst = threadManager.getIsFirstMessage()

        return chatService.createChatRequest(
            modelId: selectedModelId,
            message: message,
            threadId: threadId,
            thinking: enableThinking,
            networking: enableNetworking,
            isFirst: isFirst
        )
    }

    private func createRetryRequest(message: String) -> UnifiedChatCompletionReq {
        let threadId = threadManager.getCurrentThreadId()

        return chatService.createChatRequest(
            modelId: selectedModelId,
            message: message,
            threadId: threadId,
            thinking: enableThinking,
            networking: enableNetworking,
            parentId: threadId,
            isFirst: false
        )
    }

    // MARK: - 重试状态管理

    private func setupRetryState(for message: ChatMessageModel) {
        isRetryingMessage = true
        retryingMessageId = message.id
        retryingVariantIndex = message.currentVariantIndex
        objectWillChange.send()
    }

    // MARK: - 流式响应处理

    private func handleStreamResponse(_ response: Res<ChatCompletionPes>) {
        // 处理试用结束错误
        if response.code == 10000 {
            var content = ""
            switch response.data {
            case .text(_, let textContent):
                content = textContent
            default:
                content = response.msg
            }
            handleTrialEndedError(content)
            return
        }

        // 处理其他错误
        guard response.code == 200 else {
            handleError(BusinessError(code: response.code, message: response.msg))
            return
        }

        // 处理成功响应
        let chatData = response.data

        switch chatData {
        case .text(_, let content):
            accumulateTextContent(content)
        case .reasoningText(_, let content):
            accumulateReasoningContent(content)
        case .toolDeepSearch(_, let searchResults):
            accumulateSearchResults(searchResults)
        case .event(_, let type, let event, let node, let content):
            // 安全地将 AnyCodable? 转换为 String?
            let contentString = content?.value as? String
            handleEventResponse(type: type, event: event, node: node, content: contentString)
        }
    }

    // MARK: - 积累流式消息

    private func accumulateTextContent(_ content: String) {
        guard streamingMessage != nil else {
            ensureStreamingMessage()
            return
        }

        streamingManager.appendContent(content) { [weak self] bufferedContent in
            self?.updateStreamingMessageContent(bufferedContent)
        }
    }

    private func accumulateReasoningContent(_ content: String) {
        guard streamingMessage != nil else {
            ensureStreamingMessage()
            return
        }

        guard let currentMessage = streamingMessage else { return }

        let updatedReasoningContent = (currentMessage.reasoningContent ?? "") + content

        streamingMessage = ChatMessageModel(
            id: currentMessage.id,
            content: currentMessage.content,
            isUser: false,
            type: .reasoning,
            modelId: currentMessage.modelId,
            searchResults: currentMessage.searchResults,
            files: currentMessage.files,
            images: currentMessage.images,
            reasoningContent: updatedReasoningContent,
            toolCallStatus: currentMessage.toolCallStatus,
            isToolActive: currentMessage.isToolActive,
            variants: currentMessage.variants,
            currentVariantIndex: currentMessage.currentVariantIndex,
            chatMode: currentMessage.chatMode
        )
    }

    private func accumulateSearchResults(_ searchResults: [SearchResult]) {
        guard streamingMessage != nil else {
            ensureStreamingMessage()
            return
        }

        guard let currentMessage = streamingMessage else { return }

        var allSearchResults = currentMessage.searchResults ?? []
        for result in searchResults {
            if !allSearchResults.contains(where: { $0.url == result.url }) {
                allSearchResults.append(result)
            }
        }

        streamingMessage = ChatMessageModel(
            id: currentMessage.id,
            content: currentMessage.content,
            isUser: false,
            type: .searchResult,
            modelId: currentMessage.modelId,
            searchResults: allSearchResults,
            files: currentMessage.files,
            images: currentMessage.images,
            reasoningContent: currentMessage.reasoningContent,
            toolCallStatus: currentMessage.toolCallStatus,
            isToolActive: currentMessage.isToolActive,
            variants: currentMessage.variants,
            currentVariantIndex: currentMessage.currentVariantIndex,
            chatMode: currentMessage.chatMode
        )
    }

    private func updateStreamingMessageContent(_ bufferedContent: String) {
        guard let currentMessage = streamingMessage else { return }

        let updatedContent = currentMessage.content + bufferedContent

        streamingContent = updatedContent

        // 处理工具调用状态
        var updatedToolCallStatus = currentMessage.toolCallStatus
        var updatedIsToolActive = currentMessage.isToolActive

        if let toolStatus = currentMessage.toolCallStatus, !toolStatus.isEmpty, currentMessage.isToolActive {
            updatedIsToolActive = false
            updatedToolCallStatus = "工具调用完毕"
        }

        streamingMessage = ChatMessageModel(
            id: currentMessage.id,
            content: updatedContent,
            isUser: false,
            type: .text,
            modelId: currentMessage.modelId,
            searchResults: currentMessage.searchResults,
            files: currentMessage.files,
            images: currentMessage.images,
            reasoningContent: currentMessage.reasoningContent,
            toolCallStatus: updatedToolCallStatus,
            isToolActive: updatedIsToolActive,
            variants: currentMessage.variants,
            currentVariantIndex: currentMessage.currentVariantIndex,
            chatMode: currentMessage.chatMode
        )
    }

    private func ensureStreamingMessage() {
        if streamingMessage == nil {
            streamingMessage = createStreamingMessage()
            currentStreamingMessageId = streamingMessage?.id
        }
    }

    // MARK: - 事件处理

    private func handleEventResponse(type: EventResponse.EventType, event: String?, node: String?, content: String?) {
        guard streamingMessage != nil else {
            ensureStreamingMessage()
            return
        }

        switch type {
        case .status:
            // 处理状态事件（如工具调用状态）
            if event == "on_tool_start" {
                let toolStatus = content ?? "工具调用中..."
                updateStreamingMessageToolStatus(status: toolStatus, isActive: true)
            } else if event == "on_tool_end" {
                let toolStatus = content ?? "工具调用完毕"
                updateStreamingMessageToolStatus(status: toolStatus, isActive: false)
            }

        case .ragIndex:
            // 对于文本聊天，通常不需要特殊处理 RAG 索引事件
            break
        }
    }

    /// 更新流式消息的工具调用状态
    private func updateStreamingMessageToolStatus(status: String, isActive: Bool) {
        guard let currentMessage = streamingMessage else { return }

        streamingMessage = ChatMessageModel(
            id: currentMessage.id,
            content: currentMessage.content,
            isUser: false,
            type: currentMessage.type,
            modelId: currentMessage.modelId,
            searchResults: currentMessage.searchResults,
            files: currentMessage.files,
            images: currentMessage.images,
            reasoningContent: currentMessage.reasoningContent,
            toolCallStatus: status,
            isToolActive: isActive,
            variants: currentMessage.variants,
            currentVariantIndex: currentMessage.currentVariantIndex,
            chatMode: currentMessage.chatMode
        )
    }

    private func handleStreamCompleted() {
        // 强制刷新缓冲区确保最后部分内容不丢失
        streamingManager.forceFlush { [weak self] remainingContent in
            if !remainingContent.isEmpty {
                self?.updateStreamingMessageContent(remainingContent)
            }
        }

        finalizeCurrentStreamingMessage()

        isLoading = false
        loadingType = .none
        streamingManager.reset()

        threadManager.continueCurrentSession()

        if isRetryingMessage {
            finishRetryProcess()
        }
    }

    private func handleRetryCompleted() {
        // 强制刷新缓冲区确保最后部分内容不丢失
        streamingManager.forceFlush { [weak self] remainingContent in
            if !remainingContent.isEmpty {
                self?.updateStreamingMessageContent(remainingContent)
            }
        }

        if let retryingId = retryingMessageId,
           let streaming = streamingMessage
        {
            finishRetryAsVariant(retryingId: retryingId, newMessage: streaming)
        }

        isLoading = false
        loadingType = .none
        streamingMessage = nil
        currentStreamingMessageId = nil
        streamingManager.reset()

        threadManager.continueCurrentSession()
        finishRetryProcess()
    }

    private func finishRetryProcess() {
        isRetryingMessage = false
        retryingMessageId = nil
        retryingVariantIndex = nil
        objectWillChange.send()
    }

    private func finishRetryAsVariant(retryingId: UUID, newMessage: ChatMessageModel) {
        guard let messageIndex = findMessageIndex(by: retryingId) else { return }

        let originalMessage = stableMessages[messageIndex]
        var allVariants = originalMessage.variants ?? []

        if allVariants.isEmpty {
            let originalVariant = MessageVariantModel(
                id: UUID().uuidString,
                content: originalMessage.content,
                modelId: originalMessage.modelId,
                thinkingContent: originalMessage.reasoningContent,
                searchResults: originalMessage.searchResults,
                files: originalMessage.files,
                images: originalMessage.images
            )
            allVariants.append(originalVariant)
        }

        let newVariant = MessageVariantModel(
            id: UUID().uuidString,
            content: newMessage.content,
            modelId: newMessage.modelId,
            thinkingContent: newMessage.reasoningContent,
            searchResults: newMessage.searchResults,
            files: newMessage.files,
            images: newMessage.images
        )
        allVariants.append(newVariant)

        let currentVariantIndex = retryingVariantIndex ?? (allVariants.count - 1)
        let displayVariant = currentVariantIndex < allVariants.count ?
            allVariants[currentVariantIndex] : allVariants.last!

        let updatedMessage = ChatMessageModel(
            id: originalMessage.id,
            content: displayVariant.content,
            isUser: originalMessage.isUser,
            type: originalMessage.type,
            modelId: displayVariant.modelId,
            searchResults: displayVariant.searchResults,
            files: displayVariant.files,
            images: displayVariant.images,
            reasoningContent: displayVariant.thinkingContent,
            toolCallStatus: newMessage.toolCallStatus,
            isToolActive: newMessage.isToolActive,
            variants: allVariants,
            currentVariantIndex: currentVariantIndex,
            chatMode: originalMessage.chatMode
        )

        stableMessages[messageIndex] = updatedMessage
        messageManager.updateIndexCache(for: stableMessages)
    }

    // MARK: - 错误处理

    private func handleError(_ error: BusinessError) {
        // 强制刷新缓冲区确保最后部分内容不丢失
        streamingManager.forceFlush { [weak self] remainingContent in
            if !remainingContent.isEmpty {
                self?.updateStreamingMessageContent(remainingContent)
            }
        }

        isLoading = false
        loadingType = .none
        errorMessage = error.message
        showError = true

        // 清理流式状态
        streamingMessage = nil
        currentStreamingMessageId = nil
        streamingContent = ""
        streamingManager.reset()

        // 清理重试状态
        if isRetryingMessage {
            finishRetryProcess()
        }
    }

    private func handleTrialEndedError(_ content: String) {
        // 强制刷新缓冲区确保最后部分内容不丢失
        streamingManager.forceFlush { [weak self] remainingContent in
            if !remainingContent.isEmpty {
                self?.updateStreamingMessageContent(remainingContent)
            }
        }

        let trialEndedMessage = ChatMessageModel(
            content: content,
            isUser: false,
            type: .trialEnded,
            modelId: selectedModelId,
            chatMode: .agent
        )

        stableMessages.append(trialEndedMessage)
        messageManager.updateIndexCache(for: stableMessages)

        // 重置状态
        isLoading = false
        loadingType = .none
        streamingMessage = nil
        currentStreamingMessageId = nil
        streamingContent = ""
        streamingManager.reset()

        if isRetryingMessage {
            finishRetryProcess()
        }
    }
}

// MARK: - 性能优化组件

/// 流式内容管理器 - 负责高效的流式内容处理
private class StreamingContentManager: ObservableObject {
    private var contentBuffer = ""
    private var bufferTimer: Timer?
    private let bufferFlushInterval: TimeInterval = 0.05 // 50ms 缓冲
    private let maxBufferSize = 100 // 最大缓冲字符数

    func appendContent(_ content: String, completion: @escaping (String) -> Void) {
        contentBuffer += content

        // 达到缓冲阈值或定时器触发时刷新
        if contentBuffer.count >= maxBufferSize {
            flushBuffer(completion: completion)
        } else {
            scheduleBufferFlush(completion: completion)
        }
    }

    private func scheduleBufferFlush(completion: @escaping (String) -> Void) {
        bufferTimer?.invalidate()
        bufferTimer = Timer.scheduledTimer(withTimeInterval: bufferFlushInterval, repeats: false) { [weak self] _ in
            self?.flushBuffer(completion: completion)
        }
    }

    private func flushBuffer(completion: @escaping (String) -> Void) {
        guard !contentBuffer.isEmpty else { return }

        let content = contentBuffer
        contentBuffer = ""
        bufferTimer?.invalidate()
        bufferTimer = nil

        completion(content)
    }

    func reset() {
        contentBuffer = ""
        bufferTimer?.invalidate()
        bufferTimer = nil
    }

    func forceFlush(completion: @escaping (String) -> Void) {
        flushBuffer(completion: completion)
    }
}

/// 消息数据管理器 - 负责消息的高效存储和检索（性能优化版本）
private class MessageDataManager {
    private var messageIndexCache: [UUID: Int] = [:]
    private var lastCacheSize = 0

    /// 增量更新索引缓存
    func updateIndexCache(for messages: [ChatMessageModel]) {
        let currentSize = messages.count

        if currentSize == lastCacheSize, !messageIndexCache.isEmpty {
            return
        }

        if currentSize > lastCacheSize, lastCacheSize > 0 {
            for index in lastCacheSize..<currentSize {
                if index < messages.count {
                    messageIndexCache[messages[index].id] = index
                }
            }
        } else {
            messageIndexCache.removeAll(keepingCapacity: true)
            for (index, message) in messages.enumerated() {
                messageIndexCache[message.id] = index
            }
        }

        lastCacheSize = currentSize
    }

    /// 添加单个消息到缓存
    func addMessageToCache(message: ChatMessageModel, at index: Int) {
        messageIndexCache[message.id] = index
        lastCacheSize = max(lastCacheSize, index + 1)
    }

    /// 清空缓存
    func clearCache() {
        messageIndexCache.removeAll(keepingCapacity: true)
        lastCacheSize = 0
    }

    func findMessageIndex(by id: UUID, in messages: [ChatMessageModel]) -> Int? {
        if let cachedIndex = messageIndexCache[id],
           cachedIndex < messages.count,
           messages[cachedIndex].id == id
        {
            return cachedIndex
        }

        if let index = messages.firstIndex(where: { $0.id == id }) {
            messageIndexCache[id] = index
            return index
        }

        return nil
    }
}
